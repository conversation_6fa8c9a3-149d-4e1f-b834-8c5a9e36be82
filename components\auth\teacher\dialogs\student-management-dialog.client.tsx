'use client';

import { useState, useRef, useCallback } from 'react';
import { useMutation } from 'blade/client/hooks';
import { useAuth } from '../../../../hooks/useAuth';
import { Dialog } from '@base-ui-components/react/dialog';
import { Switch } from '@base-ui-components/react/switch';
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
  TabsContents,
} from '../../../ui/animate-ui/components/tabs';
import { UserPlus, X, Users, School, Trash2, Check } from 'lucide-react';
import { cn } from '../../../../lib/utils';
import {
  InputButton,
  InputButtonProvider,
  InputButtonAction,
  InputButtonSubmit,
  InputButtonInput,
} from '../../../ui/buttons/input';
import { motion, AnimatePresence } from 'motion/react';

interface PendingStudent {
  id: string;
  name: string;
  email: string;
  generateUsername: boolean;
  classes: string[];
}

interface ClassItem {
  id: string;
  name: string;
  studentCount: number;
  maxCapacity: number;
}

interface StudentManagementDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

type StudentFormStep = 'name' | 'email';
type DialogTab = 'invite' | 'classes';

export function StudentManagementDialog({ isOpen, onOpenChange }: StudentManagementDialogProps) {
  // Tab and form state
  const [activeTab, setActiveTab] = useState<DialogTab>('invite');
  const [currentStep, setCurrentStep] = useState<StudentFormStep>('name');
  const [showInput, setShowInput] = useState(false);

  // Form data
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [generateUsername, setGenerateUsername] = useState(true);

  // Students and classes
  const [pendingStudents, setPendingStudents] = useState<PendingStudent[]>([]);
  const [selectedClass, setSelectedClass] = useState<string | null>(null);
  const [studentClassAssignments, setStudentClassAssignments] = useState<Record<string, string[]>>({});

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);

  // Refs
  const inputRef = useRef<HTMLDivElement>(null);
  const messageTimeoutRef = useRef<NodeJS.Timeout>();

  const { user } = useAuth();
  const { set } = useMutation();

  // Mock classes data - in real implementation, this would come from the teacher's classes
  const teacherClasses: ClassItem[] = [
    { id: '1', name: 'Math 101', studentCount: 25, maxCapacity: 30 },
    { id: '2', name: 'Physics Advanced', studentCount: 18, maxCapacity: 25 },
    { id: '3', name: 'Chemistry Basics', studentCount: 22, maxCapacity: 28 },
  ];

  // Utility functions
  const createUsername = useCallback((name: string, email: string): string => {
    // Create username from name and email
    const namePart = name.toLowerCase().replace(/\s+/g, '');
    const emailPart = email.split('@')[0]?.toLowerCase() || '';
    const randomNum = Math.floor(Math.random() * 100).toString().padStart(2, '0');

    // Prefer name-based username, fallback to email-based
    const baseUsername = namePart.length >= 3 ? namePart : emailPart;
    return `${baseUsername}${randomNum}`;
  }, []);

  const resetForm = useCallback(() => {
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current);
    }
    setName('');
    setEmail('');
    setCurrentStep('name');
    setShowInput(false);
    setMessage('');
    setMessageType('');
  }, []);

  const resetDialog = useCallback(() => {
    resetForm();
    setPendingStudents([]);
    setSelectedClass(null);
    setStudentClassAssignments({});
    setActiveTab('invite');
    setShowCancelConfirmation(false);
  }, [resetForm]);

  // Handle dialog close with confirmation if needed
  const handleDialogClose = useCallback((open: boolean) => {
    if (!open && pendingStudents.length > 0 && !showCancelConfirmation) {
      setShowCancelConfirmation(true);
      return;
    }

    if (!open) {
      resetDialog();
    }

    onOpenChange(open);
  }, [pendingStudents.length, showCancelConfirmation, resetDialog, onOpenChange]);

  const handleCancelConfirm = useCallback(() => {
    setShowCancelConfirmation(false);
    resetDialog();
    onOpenChange(false);
  }, [resetDialog, onOpenChange]);

  const handleCancelDeny = useCallback(() => {
    setShowCancelConfirmation(false);
  }, []);

  // Form submission handlers
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (currentStep === 'name') {
      if (!showInput) {
        setShowInput(true);
        return;
      }

      if (!name.trim()) {
        setMessage('Please enter a student name');
        setMessageType('error');

        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 3000);
        return;
      }

      setCurrentStep('email');
      return;
    }

    if (currentStep === 'email') {
      if (!email.trim() || !email.includes('@')) {
        setMessage('Please enter a valid email address');
        setMessageType('error');

        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 3000);
        return;
      }

      // Check if email already exists in pending students
      if (pendingStudents.some(student => student.email === email)) {
        setMessage('This email is already added');
        setMessageType('error');

        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 3000);
        return;
      }

      // Add student to pending list
      const newStudent: PendingStudent = {
        id: Date.now().toString(),
        name: name.trim(),
        email: email.trim(),
        generateUsername,
        classes: []
      };

      setPendingStudents(prev => [...prev, newStudent]);

      // Reset form for next student
      setName('');
      setEmail('');
      setCurrentStep('name');
      setShowInput(false);

      setMessage('Student added successfully!');
      setMessageType('success');

      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 2000);
    }
  };

  // Student and class management handlers
  const removeStudent = (studentId: string) => {
    setPendingStudents(prev => prev.filter(s => s.id !== studentId));
    // Also remove from class assignments
    setStudentClassAssignments(prev => {
      const updated = { ...prev };
      delete updated[studentId];
      return updated;
    });
  };

  const toggleStudentInClass = (studentId: string, classId: string) => {
    setStudentClassAssignments(prev => {
      const studentClasses = prev[studentId] || [];
      const isInClass = studentClasses.includes(classId);

      return {
        ...prev,
        [studentId]: isInClass
          ? studentClasses.filter(id => id !== classId)
          : [...studentClasses, classId]
      };
    });
  };

  const handleInviteStudents = async () => {
    if (pendingStudents.length === 0) {
      setMessage('No students to invite');
      setMessageType('error');
      return;
    }

    setIsLoading(true);

    try {
      if (!user || user.role !== 'teacher') {
        throw new Error('Only teachers can add students');
      }

      // Process each student
      for (const student of pendingStudents) {
        // Only generate username if the student has the generateUsername flag set to true
        const username = student.generateUsername ? createUsername(student.name, student.email) : undefined;

        // Generate a unique password for this student
        const generateStudentPassword = () => {
          const adjectives = ['Smart', 'Bright', 'Quick', 'Sharp', 'Clever', 'Wise', 'Bold', 'Swift'];
          const nouns = ['Lion', 'Eagle', 'Tiger', 'Wolf', 'Bear', 'Fox', 'Hawk', 'Owl'];
          const numbers = Math.floor(Math.random() * 100).toString().padStart(2, '0');

          const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
          const noun = nouns[Math.floor(Math.random() * nouns.length)];

          return `${adjective}${noun}${numbers}`;
        };

        const uniquePassword = generateStudentPassword();
        const assignedClasses = studentClassAssignments[student.id] || [];

        const response = await fetch('/api/create-student', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            email: student.email,
            password: uniquePassword,
            name: student.name,
            username,
            teacherId: user.id,
            classes: assignedClasses
          })
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
          throw new Error(`Failed to create ${student.name}: ${errorData.error || 'Unknown error'}`);
        }

        const result = await response.json();

        // Trigger UI update
        await set.users({
          with: { id: result.user.id },
          to: { updatedAt: new Date().toISOString() }
        });
      }

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('studentsCreated', {
        detail: { count: pendingStudents.length }
      }));

      setMessage(`Successfully invited ${pendingStudents.length} student${pendingStudents.length > 1 ? 's' : ''}!`);
      setMessageType('success');

      // Reset and close dialog after success
      setTimeout(() => {
        resetDialog();
        onOpenChange(false);
      }, 2000);

    } catch (error) {
      console.error('Error inviting students:', error);
      setMessage(error instanceof Error ? error.message : 'Failed to invite students. Please try again.');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  // Mouse handlers for input animation
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!inputRef.current) return;
    const rect = inputRef.current.getBoundingClientRect();
    setPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
  };

  const handleMouseEnter = () => setOpacity(1);
  const handleMouseLeave = () => setOpacity(0);

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleDialogClose}>
      <Dialog.Portal>
        <Dialog.Backdrop className="fixed inset-0 bg-black/50 opacity-100 transition-all duration-150 data-[ending-style]:opacity-0 data-[starting-style]:opacity-0 z-[100000]" />
        <Dialog.Popup className="fixed top-1/2 left-1/2 -mt-8 w-[96vw] md:max-w-[400px] max-w-[calc(100vw-3rem)] -translate-x-1/2 -translate-y-1/2 rounded-lg bg-gradient-to-r from-[#f2f2f2] via-[#e8e8e8] to-[#eeeeee] dark:from-[#101012] dark:via-[#18181a] dark:to-[#171719] text-gray-900 dark:text-gray-100 outline-1 outline-gray-200 dark:outline-gray-700 transition-all duration-150 data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 z-[100001] max-h-[90vh] overflow-hidden">

         
          {/* Tabs */}
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as DialogTab)}
            className="w-full md:w-[400px] bg-muted rounded-lg"
          >
            <div className="pb-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger
                  value="invite"
                  className="flex h-9 rounded-xl items-center justify-center px-4 py-1.5 text-sm font-manrope_1 text-black/70 dark:text-white/70 data-[state=active]:text-black/90 dark:data-[state=active]:text-white/90 data-[state=active]:bg-white/80 dark:data-[state=active]:bg-black/20 data-[state=active]:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:data-[state=active]:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200"
                >
                  <UserPlus className="w-4 h-4 mr-2" />
                  Invite Students
                </TabsTrigger>
                <TabsTrigger
                  value="classes"
                  className="flex h-9 rounded-xl items-center justify-center px-4 py-1.5 text-sm font-manrope_1 text-black/70 dark:text-white/70 data-[state=active]:text-black/90 dark:data-[state=active]:text-white/90 data-[state=active]:bg-white/80 dark:data-[state=active]:bg-black/20 data-[state=active]:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:data-[state=active]:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200"
                >
                  <School className="w-4 h-4 mr-2" />
                  Assign Classes
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContents className="mx-1 mb-1 -mt-2 rounded-sm h-full bg-background">
              {/* Invite Tab Panel */}
              <TabsContent value="invite" className="flex-1 px-6 pb-6 overflow-y-auto h-full">
              <div className="space-y-6">
                {/* Student Input Form */}
                <div className="space-y-4">
                  <div className="flex items-center  justify-between">
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Add Students</h3>
                    <div className="flex items-center gap-2">
                     
                      <span className="text-xs font-manrope_1 text-gray-600 dark:text-gray-400">
                        {generateUsername ? 'Generate username for login' : 'Use email for login'}
                      </span>
                       <Switch.Root
                        checked={generateUsername}
                        onCheckedChange={setGenerateUsername}
                        className="relative flex h-5 w-9 rounded-full bg-gray-200 dark:bg-gray-700 p-px transition-colors data-[checked]:bg-blue-600"
                      >
                        <Switch.Thumb className="aspect-square h-full rounded-full bg-white transition-transform data-[checked]:translate-x-4" />
                      </Switch.Root>
                    </div>
                  </div>

                  <form onSubmit={handleFormSubmit} className="relative">
                    <InputButtonProvider
                      showInput={showInput}
                      setShowInput={setShowInput}
                      transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                      className="!w-full !max-w-[400px] relative group items-center justify-center h-12 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
                      onMouseMove={handleMouseMove}
                      onMouseEnter={handleMouseEnter}
                      onMouseLeave={handleMouseLeave}
                    >
                      <InputButton>
                        <AnimatePresence>
                          {!showInput && (
                            <motion.div
                              key="action-text"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              exit={{ opacity: 0 }}
                              transition={{ duration: 0.3 }}
                              className="w-full h-full"
                            >
                              <InputButtonAction>
                                <div className="flex items-center gap-2">
                                  <UserPlus className="w-4 h-4" />
                                  {currentStep === 'name' ? 'Add Student Name' : 'Add Student Email'}
                                </div>
                              </InputButtonAction>
                            </motion.div>
                          )}
                        </AnimatePresence>

                        <InputButtonSubmit
                          onClick={() => {}}
                          type="submit"
                          disabled={isLoading}
                          message={message}
                          messageType={messageType}
                          isSubmitting={false}
                          success={false}
                        >
                          {currentStep === 'name' ? 'Next' : 'Add Student'}
                        </InputButtonSubmit>

                        {showInput && (
                          <div className="flex items-center w-full pl-0">
                            <InputButtonInput
                              type={currentStep === 'name' ? 'text' : 'email'}
                              placeholder={currentStep === 'name' ? 'Enter student name' : 'Enter student email'}
                              value={currentStep === 'name' ? name : email}
                              onChange={(e) => currentStep === 'name' ? setName(e.target.value) : setEmail(e.target.value)}
                              disabled={isLoading}
                              required
                              autoFocus
                            />
                          </div>
                        )}

                        <div
                          ref={inputRef}
                          className="pointer-events-none absolute inset-0 rounded-full border-2 border-orange-500/50 transition-opacity duration-500"
                          style={{
                            opacity: opacity,
                            WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                            maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                          } as React.CSSProperties}
                        />
                      </InputButton>
                    </InputButtonProvider>
                  </form>
                </div>

                {/* Pending Students List */}
                {pendingStudents.length > 0 && (
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Students to Invite ({pendingStudents.length})
                    </h3>
                    <div className="space-y-2 max-h-60 custom-scrollbar overflow-y-auto">
                      {pendingStudents.map((student) => (
                        <div
                          key={student.id}
                          className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div className="flex-1">
                            <div className="font-medium text-sm">{student.name}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">{student.email}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              Login: {student.generateUsername ? 'Username will be generated' : 'Will use email'}
                            </div>
                            {(studentClassAssignments[student.id]?.length || 0) > 0 && (
                              <div className="flex gap-1 mt-1">
                                {(studentClassAssignments[student.id] || []).map(classId => {
                                  const className = teacherClasses.find(c => c.id === classId)?.name;
                                  return (
                                    <span key={classId} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                      {className}
                                    </span>
                                  );
                                })}
                              </div>
                            )}
                          </div>
                          <button
                            onClick={() => removeStudent(student.id)}
                            className="p-1 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              </TabsContent>

              {/* Classes Tab Panel */}
              <TabsContent value="classes" className="flex-1 px-6 pb-6 overflow-y-auto h-full">
              <div className="space-y-6">
                {pendingStudents.length === 0 ? (
                  <div className="text-center py-8">
                    <School className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                    <p className="font-manrope_1 text-black/80 dark:text-white/80">
                      Add students in the Invite tab first to assign them to classes.
                    </p>
                  </div>
                ) : selectedClass ? (
                  /* Class Detail View */
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setSelectedClass(null)}
                        className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-manrope_1"
                      >
                        ← Back to Classes
                      </button>
                    </div>

                    {(() => {
                      const classInfo = teacherClasses.find(c => c.id === selectedClass);
                      return classInfo ? (
                        <div>
                          <h3 className="text-lg font-manrope_1 font-medium text-gray-900 dark:text-gray-100 mb-2">
                            {classInfo.name}
                          </h3>
                          <p className="text-sm font-manrope_1 text-gray-600 dark:text-gray-400 mb-4">
                            {classInfo.studentCount}/{classInfo.maxCapacity} students
                          </p>

                          <div className="space-y-3">
                            <h4 className="text-sm font-manrope_1 font-medium text-gray-700 dark:text-gray-300">
                              Select students for this class:
                            </h4>
                            {pendingStudents.map((student) => {
                              const isInClass = studentClassAssignments[student.id]?.includes(selectedClass) || false;
                              return (
                                <div
                                  key={student.id}
                                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                                >
                                  <div className="flex-1">
                                    <div className="font-medium font-manrope_1 text-sm">{student.name}</div>
                                    <div className="text-xs text-gray-500 dark:text-gray-400">{student.email}</div>
                                  </div>
                                  <button
                                    onClick={() => toggleStudentInClass(student.id, selectedClass)}
                                    className={cn(
                                      "flex items-center font-manrope_1 justify-center w-6 h-6 rounded border-2 transition-colors",
                                      isInClass
                                        ? "bg-blue-600 border-blue-600 text-white"
                                        : "border-gray-300 dark:border-gray-600 hover:border-blue-600"
                                    )}
                                  >
                                    {isInClass && <Check className="w-4 h-4" />}
                                  </button>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      ) : null;
                    })()}
                  </div>
                ) : (
                  /* Classes List View */
                  <div className="space-y-4">
                    <h3 className="text-sm font-manrope_1 font-medium text-gray-700 dark:text-gray-300">
                      Select a class to assign students:
                    </h3>
                    <div className="grid gap-3">
                      {teacherClasses.map((classItem) => {
                        const assignedStudents = pendingStudents.filter(student =>
                          studentClassAssignments[student.id]?.includes(classItem.id)
                        ).length;

                        return (
                          <button
                            key={classItem.id}
                            onClick={() => setSelectedClass(classItem.id)}
                            className="flex font-manrope_1 items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors text-left"
                          >
                            <div className="flex-1">
                              <div className="font-medium text-sm">{classItem.name}</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {classItem.studentCount}/{classItem.maxCapacity} current students
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {assignedStudents > 0 && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                  +{assignedStudents}
                                </span>
                              )}
                              <div className="text-gray-400">→</div>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
              </TabsContent>
            </TabsContents>

            {/* Footer with Invite Button */}
            <div className="px-6 py-4 border-t border-black/10 dark:border-white/10  ">
              <div className="flex justify-between items-center">
                <div className="text-xs font-manrope_1 text-black/80 dark:text-white/80">
                  {showCancelConfirmation ? (
                    'You will lose all added students. Are you sure?'
                  ) : pendingStudents.length > 0 ? (
                    `${pendingStudents.length} student${pendingStudents.length > 1 ? 's' : ''} ready to invite`
                  ) : (
                    'No students added yet'
                  )}
                </div>
                <div className="flex gap-2">
                  {showCancelConfirmation ? (
                    <>
                      <button
                        onClick={handleCancelDeny}
                        className="flex h-10 font-manrope_1 items-center justify-center rounded-md border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 text-xs font-medium text-gray-900 dark:text-gray-100 select-none hover:bg-gray-50 dark:hover:bg-gray-600 focus-visible:outline-2 focus-visible:-outline-offset-1 focus-visible:outline-white/10"
                      >
                        No
                      </button>
                      <button
                        onClick={handleCancelConfirm}
                        className="flex h-10 font-manrope_1 items-center justify-center rounded-md px-3 text-xs font-medium text-white select-none focus-visible:outline-2 focus-visible:-outline-offset-1 focus-visible:outline-white/10 bg-red-600/40 hover:bg-red-700/80 active:bg-red-700"
                      >
                        Yes
                      </button>
                    </>
                  ) : (
                    <>
                      <Dialog.Close className="flex h-10 text-xs font-manrope_1 items-center justify-center text-black/80 dark:text-white/80">
                        Cancel
                      </Dialog.Close>
                      <button
                        onClick={handleInviteStudents}
                        disabled={isLoading || pendingStudents.length === 0}
                        className={cn(
                          "h-10 items-center justify-center text-xs flex-1 text-center rounded-lg py-2 font-manrope_1 font-semibold text-white/80 dark:text-black/80 bg-gradient-to-b from-[#2a2a2a] via-[#1a1a1a] to-[#0a0a0a] dark:from-[#c7c7c7] dark:via-[#d9d9d9] dark:to-[#ececec] opacity-60",
                          isLoading || pendingStudents.length === 0
                            ? "text-white/80 dark:text-black/80 bg-gradient-to-b from-[#2a2a2a] via-[#1a1a1a] to-[#0a0a0a] dark:from-[#c7c7c7] dark:via-[#d9d9d9] dark:to-[#ececec] opacity-40 cursor-not-allowed"
                            : "bg-gradient-to-b from-[#2a2a2a] via-[#1a1a1a] to-[#0a0a0a] dark:from-[#c7c7c7] dark:via-[#d9d9d9] dark:to-[#ececec]"
                        )}
                      >
                        {isLoading ? 'Inviting...' : `Invite ${pendingStudents.length > 0 ? pendingStudents.length : ''} Student${pendingStudents.length !== 1 ? 's' : ''}`}
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </Tabs>

          {/* Close button in top right */}
        
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
}

// Dialog Trigger Button Component
interface StudentManagementDialogTriggerProps {
  className?: string;
  variant?: 'icon' | 'text' | 'button';
  children?: React.ReactNode;
}

export function StudentManagementDialogTrigger({
  className,
  variant = 'icon',
  children
}: StudentManagementDialogTriggerProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Default icon variant
  if (variant === 'icon') {
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className={cn(
            "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-7 w-7",
            className
          )}
          aria-label="Add new student"
        >
          <UserPlus className="h-4 w-4" />
        </button>

        <StudentManagementDialog
          isOpen={isOpen}
          onOpenChange={setIsOpen}
        />
      </>
    );
  }

  // Button variant for styled button
  if (variant === 'button') {
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className={cn(
            "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
            className
          )}
          aria-label="Add new student"
        >
          {children || (
            <>
              <UserPlus className="h-4 w-4" />
              New Student
            </>
          )}
        </button>

        <StudentManagementDialog
          isOpen={isOpen}
          onOpenChange={setIsOpen}
        />
      </>
    );
  }

  // Text variant for header button
  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className={cn(
          "inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
          className
        )}
        aria-label="Add new student"
      >
        {children || (
          <>
            <UserPlus className="h-5 w-5" />
            Students
          </>
        )}
      </button>

      <StudentManagementDialog
        isOpen={isOpen}
        onOpenChange={setIsOpen}
      />
    </>
  );
}
